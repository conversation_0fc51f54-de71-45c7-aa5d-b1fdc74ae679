.line {
  display: inline-block;
  width: 1px;
  height: 24px;
  background-color: var(--gray-150);
  margin-right: 100px;
}

.nav-link {
  color: white;
  letter-spacing: 0.3px;
}

.nav-link:hover {
  color: #a8a8a8;
}

.navbar-toggler:focus {
  box-shadow: none;
}

.navbar-toggler-icon {
  background-image: none;
  width: 35px;
  height: 2px;
  background-color: white;
  border-radius: 25px;
  position: relative;
  -webkit-border-radius: 25px;
  -moz-border-radius: 25px;
  -ms-border-radius: 25px;
  -o-border-radius: 25px;
}
.navbar-toggler-icon::before {
  content: "";
  position: absolute;
  display: block;
  width: 100%;
  height: 100%;
  top: -12px;
  background-color: white;
  border-radius: 25px;
  -webkit-border-radius: 25px;
  -moz-border-radius: 25px;
  -ms-border-radius: 25px;
  -o-border-radius: 25px;
}

.navbar-toggler-icon::after {
  content: "";
  position: absolute;
  display: block;
  width: 100%;
  height: 100%;
  bottom: -12px;
  background-color: white;
  border-radius: 25px;
  -webkit-border-radius: 25px;
  -moz-border-radius: 25px;
  -ms-border-radius: 25px;
  -o-border-radius: 25px;
}

.navbar .container div .fa-heart {
  color: white;
  font-size: 20px;
  position: relative;
}

.navbar .container div .fa-heart::after {
  content: "";
  position: absolute;
  display: block;
  width: 60%;
  height: 60%;
  top: -1px;
  right: -3px;
  background-color: red;
  border: 3px solid #121420;
  border-radius: 50%;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  -ms-border-radius: 50%;
  -o-border-radius: 50%;
}

.fa-heart:hover {
  cursor: pointer;
}

.btn-primary {
  padding: 7px 32px;
  font-weight: 500;
  margin-left: 50px;
}

/* Wallet Connection Styles */
.wallet-section {
  margin-left: 30px;
}

.wallet-info {
  text-align: right;
  line-height: 1.2;
}

.wallet-info small {
  display: block;
  font-size: 0.75rem;
}

.metamask-warning {
  max-width: 200px;
}

.metamask-warning small {
  font-size: 0.75rem;
  line-height: 1.2;
}

/* RainbowKit Button Customization */
[data-rk] {
  --rk-colors-accentColor: #007bff;
  --rk-colors-accentColorForeground: #ffffff;
  --rk-colors-connectButtonBackground: #007bff;
  --rk-colors-connectButtonText: #ffffff;
  --rk-radii-connectButton: 6px;
  --rk-fonts-body: inherit;
}

/* Custom styling for RainbowKit connect button */
[data-rk] button[data-testid="rk-connect-button"] {
  padding: 7px 32px !important;
  font-weight: 500 !important;
  font-size: 14px !important;
  border-radius: 6px !important;
}

/* Account button styling */
[data-rk] button[data-testid="rk-account-button"] {
  background: rgba(255, 255, 255, 0.1) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  color: white !important;
  padding: 8px 16px !important;
  border-radius: 6px !important;
  font-size: 14px !important;
}

[data-rk] button[data-testid="rk-account-button"]:hover {
  background: rgba(255, 255, 255, 0.15) !important;
}

@media (max-width: 992px) {
  .order {
    order: -1;
  }

  .wallet-section {
    margin-left: 0;
    margin-top: 10px;
  }
}
