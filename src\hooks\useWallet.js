import { useEffect, useState } from 'react';
import { useAccount, useChainId, useDisconnect } from 'wagmi';

export const useWallet = () => {
  const { address, isConnected, isConnecting, isDisconnected } = useAccount();
  const chainId = useChainId();
  const { disconnect } = useDisconnect();
  const [previousAddress, setPreviousAddress] = useState(null);
  const [previousChainId, setPreviousChainId] = useState(null);

  // Handle account changes
  useEffect(() => {
    if (address && address !== previousAddress) {
      if (previousAddress) {
        console.log('Account changed from', previousAddress, 'to', address);
        // You can add custom logic here for account changes
        // For example, refresh user data, clear cache, etc.
      }
      setPreviousAddress(address);
    }
  }, [address, previousAddress]);

  // Handle network changes
  useEffect(() => {
    if (chainId && chainId !== previousChainId) {
      if (previousChainId) {
        console.log('Network changed from', previousChainId, 'to', chainId);
        // You can add custom logic here for network changes
        // For example, update contract addresses, refresh data, etc.
      }
      setPreviousChainId(chainId);
    }
  }, [chainId, previousChainId]);

  // Format address for display (show first 6 and last 4 characters)
  const formatAddress = (addr) => {
    if (!addr) return '';
    return `${addr.slice(0, 6)}...${addr.slice(-4)}`;
  };

  // Get network name
  const getNetworkName = (id) => {
    const networks = {
      1: 'Ethereum',
      137: 'Polygon',
      10: 'Optimism',
      42161: 'Arbitrum',
      8453: 'Base',
      ********: 'Sepolia'
    };
    return networks[id] || `Chain ${id}`;
  };

  // Check if MetaMask is installed
  const isMetaMaskInstalled = () => {
    return typeof window !== 'undefined' && typeof window.ethereum !== 'undefined';
  };

  return {
    address,
    isConnected,
    isConnecting,
    isDisconnected,
    chainId,
    disconnect,
    formatAddress: () => formatAddress(address),
    networkName: getNetworkName(chainId),
    isMetaMaskInstalled: isMetaMaskInstalled(),
  };
};
