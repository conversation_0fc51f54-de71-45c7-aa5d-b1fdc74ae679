@import url("https://fonts.googleapis.com/css?family=Open+Sans:400,700");
@import url("https://fonts.googleapis.com/css2?family=Nanum+Gothic:wght@700&display=swap");

* {
  box-sizing: border-box;
}
@import url("https://fonts.googleapis.com/css2?family=Ranchers&display=swap");
.logo-custom {
  font-family: "Ranchers";
  font-size: 22px;
}

body {
  padding: 0;
  margin: 0;
  font-family: "Nanum Gothic", "Open Sans", sans-serif;
}

main {
  padding: 1rem;
  margin: auto;
}

form {
  display: inline;
}

.centered {
  text-align: center;
}

.image {
  height: 20rem;
}

.image img {
  height: 100%;
}

.main-header {
  width: 100%;
  height: 3.5rem;

  background: linear-gradient(
    90deg,
    rgba(2, 0, 36, 1) 0%,
    rgba(13, 13, 38, 1) 0%,
    rgba(0, 212, 255, 1) 100%
  );
  padding: 0 1.5rem;
  display: flex;
  align-items: center;
  box-shadow: 0 2px 10px rgba(6, 81, 241, 0.637);
}

.main-header__nav {
  height: 100%;
  width: 100%;
  display: none;
  align-items: center;
  justify-content: space-between;
}

.main-header__item-list {
  list-style: none;
  margin: 0;
  padding: 0;
  display: flex;
}

.main-header__item {
  margin: 0 1rem;
  padding: 1rem;
}

.main-header__item a,
.main-header__item button {
  font: inherit;
  background: transparent;
  border: none;
  text-decoration: none;
  color: white;
  cursor: pointer;
}

.main-header__item a:hover,
.main-header__item a:active,
.main-header__item a.active,
.main-header__item button:hover,
.main-header__item button:active {
  color: #ffeb3b;
}

.mobile-nav {
  width: 30rem;
  height: 100vh;
  max-width: 90%;
  position: fixed;
  left: 0;
  top: 0;
  background: white;
  z-index: 10;
  padding: 2rem 1rem 1rem 2rem;
  transform: translateX(-100%);
  transition: transform 0.3s ease-out;
}

.mobile-nav.open {
  transform: translateX(0);
}

.mobile-nav__item-list {
  list-style: none;
  display: flex;
  flex-direction: column;
  margin: 0;
  padding: 0;
}

.mobile-nav__item {
  margin: 1rem;
  padding: 0;
}

.mobile-nav__item a,
.mobile-nav__item button {
  font: inherit;
  text-decoration: none;
  color: black;
  font-size: 1.5rem;
  padding: 0.5rem 2rem;
  background: transparent;
  border: none;
  cursor: pointer;
}

.mobile-nav__item a:active,
.mobile-nav__item a:hover,
.mobile-nav__item a.active,
.mobile-nav__item button:hover,
.mobile-nav__item button:active {
  background: linear-gradient(
    90deg,
    rgba(2, 0, 36, 1) 0%,
    rgba(13, 13, 38, 1) 0%,
    rgba(0, 212, 255, 1) 100%
  );
  color: white;
  border-radius: 3px;
}

#side-menu-toggle {
  border: 1px solid white;
  font: inherit;
  padding: 0.5rem;
  display: block;
  background: transparent;
  color: white;
  cursor: pointer;
}

#side-menu-toggle:focus {
  outline: none;
}

#side-menu-toggle:active,
#side-menu-toggle:hover {
  color: #ffeb3b;
  border-color: #ffeb3b;
}

.backdrop {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  background: rgba(0, 0, 0, 0.5);
  z-index: 5;
  display: none;
}

.grid {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-around;
  align-items: stretch;
}

.card {
  box-shadow: 0 2px 8px rgba(4, 151, 196, 0.425);

  justify-content: center;
}

.card__header,
.card__content {
  padding: 1rem;
}

.card__header h1,
.card__content h1,
.card__content h2,
.card__content p {
  margin: 0;
}

.card__image {
  width: 100%;
}

.card__image img {
  width: 100%;
}

.card__actions {
  padding: 1rem;
  text-align: center;
}

.card__actions button,
.card__actions a {
  margin: 0 0.25rem;
}

.btn {
  display: inline-block;
  padding: 0.25rem 1rem;
  text-decoration: none;
  font: inherit;
  border: 1px solid #0d042e;
  color: #0d042e;
  background: white;
  border-radius: 3px;
  cursor: pointer;
}

.btn:hover,
.btn:active {
  background-color: #0d042e;
  color: white;
}

.btn.danger {
  color: red;
  border-color: red;
}

.btn.danger:hover,
.btn.danger:active {
  background: red;
  color: white;
}
.user-message {
  margin: auto;
  width: 90%;
  border: 1px solid rgb(6, 71, 97);
  padding: 0rem;

  color: rgb(6, 71, 97);
  display: flex;
  justify-content: center;
}
.user-message--error {
  background: white;
  border: 1px solid red;
  color: red;
}
.pagination {
  margin-top: 2rem;
  text-align: center;
}
.pagination a {
  text-decoration: none;
  color: rgb(4, 59, 41);
  padding: 1rem;
  border: 1px solid rgb(4, 59, 41);
  margin: 0 1rem;
  border-radius: 50%;
}
.pagination a:hover,
.pagination a:active,
.pagination a.active {
  background-color: rgb(4, 19, 59);
  color: white;
}

@media (min-width: 768px) {
  .main-header__nav {
    display: flex;
  }

  #side-menu-toggle {
    display: none;
  }
  .user-message {
    width: 30rem;
  }
}
img {
  height: 15rem;
}
