import {getDefaultConfig} from "@rainbow-me/rainbowkit";
import {
  mainnet,
  polygon,
  optimism,
  arbitrum,
  base,
  sepolia,
} from "wagmi/chains";

export const config = getDefaultConfig({
  appName: "Property Rental DApp",
  projectId:
    process.env.REACT_APP_WALLETCONNECT_PROJECT_ID || "demo-project-id", // Get this from https://cloud.walletconnect.com
  chains: [
    mainnet,
    polygon,
    optimism,
    arbitrum,
    base,
    ...(process.env.NODE_ENV === "development" ? [sepolia] : []),
  ],
  ssr: false, // If your dApp uses server side rendering (SSR)
});
