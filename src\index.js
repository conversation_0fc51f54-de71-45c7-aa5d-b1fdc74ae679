import React from "react";
import ReactDOM from "react-dom/client";
import {WagmiProvider} from "wagmi";
import {RainbowKitProvider} from "@rainbow-me/rainbowkit";
import {QueryClient, QueryClientProvider} from "@tanstack/react-query";
import App from "./App";
import {config} from "./config/wagmi";
import "./all.min.css";
import "./style.css";
import "@rainbow-me/rainbowkit/styles.css";

const queryClient = new QueryClient();

const root = ReactDOM.createRoot(document.getElementById("root"));
root.render(
  <WagmiProvider config={config}>
    <QueryClientProvider client={queryClient}>
      <RainbowKitProvider>
        <App />
      </RainbowKitProvider>
    </QueryClientProvider>
  </WagmiProvider>
);
